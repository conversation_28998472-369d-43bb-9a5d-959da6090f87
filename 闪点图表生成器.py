import pandas as pd
from datetime import datetime
import numpy as np
from openpyxl import load_workbook
from openpyxl.chart import <PERSON><PERSON><PERSON><PERSON><PERSON>, Reference, Series
from openpyxl.chart.marker import Marker
from openpyxl.chart.axis import DateAxis
from openpyxl.styles import Font, PatternFill
import os
import shutil

def read_excel_data(file_path):
    """读取Excel数据"""
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        print(f"成功读取Excel文件，共{len(df)}行数据")
        print("列名：", df.columns.tolist())
        return df
    except Exception as e:
        print(f"读取Excel文件时出错：{e}")
        return None

def create_flash_chart(df, input_file='数据.xlsx'):
    """在原始Excel文件中创建闪点图表"""
    
    try:
        # 检查必要的列是否存在
        if len(df.columns) < 10:
            print("数据列数不足，请检查Excel文件格式")
            return
        
        # 获取A列、D列、J列数据
        # A列：状态（OK/NG）
        # D列：数据值
        # J列：日期
        
        status_col = df.iloc[:, 0]  # A列
        data_col = df.iloc[:, 3]    # D列
        date_col = df.iloc[:, 9]    # J列
        
        # 清理数据，找到有效的数据行
        valid_rows = []
        for i in range(len(df)):
            status = str(status_col.iloc[i]).strip().upper()
            
            # 检查状态是否为OK或NG
            if status in ['OK', 'NG']:
                try:
                    # 尝试转换数据值
                    value = float(data_col.iloc[i])
                    
                    # 尝试转换日期
                    date_val = date_col.iloc[i]
                    if pd.notna(date_val):
                        valid_rows.append({
                            'row_index': i + 2,  # Excel行号（从1开始，加上表头）
                            'status': status,
                            'value': value,
                            'date': date_val
                        })
                except:
                    continue
        
        if not valid_rows:
            print("没有找到有效数据，请检查数据格式")
            return
        
        print(f"有效数据点：{len(valid_rows)}")
        ok_rows = [row for row in valid_rows if row['status'] == 'OK']
        ng_rows = [row for row in valid_rows if row['status'] == 'NG']
        print(f"OK数据点：{len(ok_rows)}")
        print(f"NG数据点：{len(ng_rows)}")
        
        # 先备份原文件
        import shutil
        backup_file = input_file.replace('.xlsx', '_backup.xlsx')
        shutil.copy2(input_file, backup_file)
        print(f"已创建备份文件：{backup_file}")
        
        # 打开原始Excel文件
        wb = load_workbook(input_file)
        ws = wb.active
        
        # 在空白区域添加图表统计信息（不影响原数据）
        # 找到数据的最后一列后面添加信息
        stats_col = len(df.columns) + 2  # 在数据后面留一列空白
        
        ws.cell(row=1, column=stats_col, value='闪点图表统计')
        ws.cell(row=1, column=stats_col).font = Font(size=14, bold=True)
        
        ws.cell(row=3, column=stats_col, value=f'总数据点数：{len(valid_rows)}')
        ws.cell(row=4, column=stats_col, value=f'OK数据点数：{len(ok_rows)}')
        ws.cell(row=5, column=stats_col, value=f'NG数据点数：{len(ng_rows)}')
        if len(valid_rows) > 0:
            ws.cell(row=6, column=stats_col, value=f'OK比例：{len(ok_rows)/len(valid_rows)*100:.1f}%')
            ws.cell(row=7, column=stats_col, value=f'NG比例：{len(ng_rows)/len(valid_rows)*100:.1f}%')
        
        # 创建散点图 - 使用更安全的方法
        chart = ScatterChart()
        chart.title = "闪点图表"
        chart.style = 2
        chart.x_axis.title = '日期'
        chart.y_axis.title = '数值'
        
        # 创建两个独立的数据系列：一个给OK，一个给NG
        # 我们将在辅助列中准备数据
        
        # 在统计信息下方准备图表数据
        chart_data_start_row = 10
        
        # 添加OK数据
        if len(ok_rows) > 0:
            ws.cell(row=chart_data_start_row, column=stats_col, value='OK数据')
            ws.cell(row=chart_data_start_row + 1, column=stats_col, value='日期')
            ws.cell(row=chart_data_start_row + 1, column=stats_col + 1, value='数值')
            
            for idx, row_info in enumerate(ok_rows):
                data_row = chart_data_start_row + 2 + idx
                # 确保日期以Excel日期格式存储
                date_cell = ws.cell(row=data_row, column=stats_col, value=row_info['date'])
                date_cell.number_format = 'yyyy/m/d h:mm:ss'
                ws.cell(row=data_row, column=stats_col + 1, value=row_info['value'])
            
            # 创建OK系列
            ok_x_ref = Reference(ws, min_col=stats_col, min_row=chart_data_start_row + 2, 
                                max_row=chart_data_start_row + 1 + len(ok_rows))
            ok_y_ref = Reference(ws, min_col=stats_col + 1, min_row=chart_data_start_row + 2, 
                                max_row=chart_data_start_row + 1 + len(ok_rows))
            
            ok_series = Series(ok_y_ref, ok_x_ref, title="OK")
            ok_series.marker = Marker('circle')
            ok_series.marker.graphicalProperties.solidFill = "000000"  # 纯黑色
            ok_series.marker.graphicalProperties.line.noFill = True   # 去掉描边
            ok_series.marker.size = 6
            ok_series.graphicalProperties.line.noFill = True  # 去掉连线
            chart.series.append(ok_series)
        
        # 添加NG数据
        ng_col_start = stats_col + 3  # 在OK数据旁边
        if len(ng_rows) > 0:
            ws.cell(row=chart_data_start_row, column=ng_col_start, value='NG数据')
            ws.cell(row=chart_data_start_row + 1, column=ng_col_start, value='日期')
            ws.cell(row=chart_data_start_row + 1, column=ng_col_start + 1, value='数值')
            
            for idx, row_info in enumerate(ng_rows):
                data_row = chart_data_start_row + 2 + idx
                # 确保日期以Excel日期格式存储
                date_cell = ws.cell(row=data_row, column=ng_col_start, value=row_info['date'])
                date_cell.number_format = 'yyyy/m/d h:mm:ss'
                ws.cell(row=data_row, column=ng_col_start + 1, value=row_info['value'])
            
            # 创建NG系列
            ng_x_ref = Reference(ws, min_col=ng_col_start, min_row=chart_data_start_row + 2, 
                                max_row=chart_data_start_row + 1 + len(ng_rows))
            ng_y_ref = Reference(ws, min_col=ng_col_start + 1, min_row=chart_data_start_row + 2, 
                                max_row=chart_data_start_row + 1 + len(ng_rows))
            
            ng_series = Series(ng_y_ref, ng_x_ref, title="NG")
            ng_series.marker = Marker('circle')
            ng_series.marker.graphicalProperties.solidFill = "FF0000"  # 纯红色
            ng_series.marker.graphicalProperties.line.noFill = True   # 去掉描边
            ng_series.marker.size = 6
            ng_series.graphicalProperties.line.noFill = True  # 去掉连线
            chart.series.append(ng_series)
        
        # 设置图表位置和大小（放在数据右侧）
        chart.width = 15
        chart.height = 10
        chart.legend.position = 'r'
        
        # 设置X轴为日期轴格式
        chart.x_axis.number_format = 'yyyy/m/d'
        chart.x_axis.majorTimeUnit = "days"
        # 设置日期轴的显示范围
        if valid_rows:
            from openpyxl.chart.axis import DateAxis
            chart.x_axis = DateAxis()
            chart.x_axis.title = '日期'
            chart.x_axis.number_format = 'yyyy/m/d'
        
        # 计算图表位置
        chart_col_index = len(df.columns) + 2
        if chart_col_index <= 26:
            chart_col = chr(ord('A') + chart_col_index - 1)
        else:
            # 处理超过Z列的情况
            first_letter = chr(ord('A') + (chart_col_index - 1) // 26 - 1)
            second_letter = chr(ord('A') + (chart_col_index - 1) % 26)
            chart_col = first_letter + second_letter
        
        ws.add_chart(chart, f"{chart_col}10")
        
        # 调整列宽以适应统计信息
        if stats_col <= 26:
            stats_col_letter = chr(ord('A') + stats_col - 1)
        else:
            first_letter = chr(ord('A') + (stats_col - 1) // 26 - 1)
            second_letter = chr(ord('A') + (stats_col - 1) % 26)
            stats_col_letter = first_letter + second_letter
        
        ws.column_dimensions[stats_col_letter].width = 20
        
        # 保存文件
        wb.save(input_file)
        
        print(f"图表已添加到原文件：{input_file}")
        print("注意：原始数据保持不变，图表和统计信息已添加到数据右侧")
        
        return valid_rows
        
    except Exception as e:
        print(f"创建图表时出错：{e}")
        # 如果出错，尝试恢复备份文件
        try:
            if 'backup_file' in locals() and os.path.exists(backup_file):
                shutil.copy2(backup_file, input_file)
                print(f"已从备份文件恢复原文件")
        except:
            pass
        return None

def main():
    # 输入文件路径
    input_file = '数据.xlsx'
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：找不到文件 {input_file}")
        return
    
    # 读取数据
    df = read_excel_data(input_file)
    if df is None:
        return
    
    # 显示前几行数据用于调试
    print("\n前5行数据预览：")
    print(df.head())
    
    # 在原文件中创建闪点图表
    result = create_flash_chart(df, input_file)
    
    if result is not None:
        print("\n图表生成完成！")
        print(f"有效数据点：{len(result)}")
        print("\n请打开原Excel文件查看图表")

if __name__ == "__main__":
    main()