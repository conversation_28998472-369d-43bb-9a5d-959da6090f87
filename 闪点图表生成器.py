import pandas as pd
from datetime import datetime
import numpy as np
from openpyxl import load_workbook
from openpyxl.chart import <PERSON><PERSON><PERSON><PERSON><PERSON>, Reference, Series
from openpyxl.chart.marker import Marker
from openpyxl.chart.axis import DateAxis
from openpyxl.styles import Font, PatternFill
import os
import shutil

def read_excel_data(file_path):
    """读取Excel数据"""
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        print(f"成功读取Excel文件，共{len(df)}行数据")
        print("列名：", df.columns.tolist())
        return df
    except Exception as e:
        print(f"读取Excel文件时出错：{e}")
        return None

def create_flash_chart_safe(df, input_file='数据.xlsx'):
    """创建一个全新的安全Excel文件，包含原数据和图表"""

    try:
        # 检查必要的列是否存在
        if len(df.columns) < 10:
            print("数据列数不足，请检查Excel文件格式")
            return

        # 获取A列、D列、J列数据
        status_col = df.iloc[:, 0]  # A列
        data_col = df.iloc[:, 3]    # D列
        date_col = df.iloc[:, 9]    # J列

        # 清理数据，找到有效的数据行
        valid_rows = []
        for i in range(len(df)):
            status = str(status_col.iloc[i]).strip().upper()

            if status in ['OK', 'NG']:
                try:
                    value = float(data_col.iloc[i])
                    if not np.isfinite(value):
                        continue

                    date_val = date_col.iloc[i]
                    if pd.notna(date_val):
                        # 简化日期处理
                        if isinstance(date_val, str):
                            try:
                                date_val = pd.to_datetime(date_val).date()
                            except:
                                continue
                        elif hasattr(date_val, 'date'):
                            date_val = date_val.date()

                        valid_rows.append({
                            'row_index': i + 2,
                            'status': status,
                            'value': value,
                            'date': date_val
                        })
                except:
                    continue

        if not valid_rows:
            print("没有找到有效数据，请检查数据格式")
            return

        print(f"有效数据点：{len(valid_rows)}")
        ok_rows = [row for row in valid_rows if row['status'] == 'OK']
        ng_rows = [row for row in valid_rows if row['status'] == 'NG']
        print(f"OK数据点：{len(ok_rows)}")
        print(f"NG数据点：{len(ng_rows)}")

        # 创建新的工作簿而不是修改原文件
        from openpyxl import Workbook
        wb = Workbook()
        ws = wb.active
        ws.title = "数据和图表"
        
        # 首先写入原始数据
        # 写入表头
        for col_idx, col_name in enumerate(df.columns, 1):
            ws.cell(row=1, column=col_idx, value=col_name)

        # 写入数据
        for row_idx, (_, row_data) in enumerate(df.iterrows(), 2):
            for col_idx, value in enumerate(row_data, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # 在数据右侧添加统计信息
        stats_col = len(df.columns) + 2

        ws.cell(row=1, column=stats_col, value='闪点图表统计')
        ws.cell(row=1, column=stats_col).font = Font(size=14, bold=True)

        ws.cell(row=3, column=stats_col, value=f'总数据点数：{len(valid_rows)}')
        ws.cell(row=4, column=stats_col, value=f'OK数据点数：{len(ok_rows)}')
        ws.cell(row=5, column=stats_col, value=f'NG数据点数：{len(ng_rows)}')
        if len(valid_rows) > 0:
            ws.cell(row=6, column=stats_col, value=f'OK比例：{len(ok_rows)/len(valid_rows)*100:.1f}%')
            ws.cell(row=7, column=stats_col, value=f'NG比例：{len(ng_rows)/len(valid_rows)*100:.1f}%')
        
        # 创建简化的散点图
        chart = ScatterChart()
        chart.title = "闪点图表"
        chart.style = 2
        chart.x_axis.title = '序号'  # 简化为序号而不是日期
        chart.y_axis.title = '数值'

        # 在统计信息下方准备简化的图表数据
        chart_data_start_row = 10
        
        # 简化的数据准备 - 使用序号代替日期
        if len(ok_rows) > 0:
            ws.cell(row=chart_data_start_row, column=stats_col, value='OK数据')
            ws.cell(row=chart_data_start_row + 1, column=stats_col, value='序号')
            ws.cell(row=chart_data_start_row + 1, column=stats_col + 1, value='数值')

            for idx, row_info in enumerate(ok_rows):
                data_row = chart_data_start_row + 2 + idx
                ws.cell(row=data_row, column=stats_col, value=idx + 1)  # 使用序号
                ws.cell(row=data_row, column=stats_col + 1, value=row_info['value'])

            # 创建OK系列
            ok_x_ref = Reference(ws, min_col=stats_col, min_row=chart_data_start_row + 2,
                                max_row=chart_data_start_row + 1 + len(ok_rows))
            ok_y_ref = Reference(ws, min_col=stats_col + 1, min_row=chart_data_start_row + 2,
                                max_row=chart_data_start_row + 1 + len(ok_rows))

            ok_series = Series(ok_y_ref, ok_x_ref, title="OK")
            ok_series.marker = Marker('circle')
            ok_series.marker.size = 5
            chart.series.append(ok_series)
        
        # 简化的NG数据
        ng_col_start = stats_col + 3
        if len(ng_rows) > 0:
            ws.cell(row=chart_data_start_row, column=ng_col_start, value='NG数据')
            ws.cell(row=chart_data_start_row + 1, column=ng_col_start, value='序号')
            ws.cell(row=chart_data_start_row + 1, column=ng_col_start + 1, value='数值')

            for idx, row_info in enumerate(ng_rows):
                data_row = chart_data_start_row + 2 + idx
                ws.cell(row=data_row, column=ng_col_start, value=len(ok_rows) + idx + 1)  # 继续序号
                ws.cell(row=data_row, column=ng_col_start + 1, value=row_info['value'])

            # 创建NG系列
            ng_x_ref = Reference(ws, min_col=ng_col_start, min_row=chart_data_start_row + 2,
                                max_row=chart_data_start_row + 1 + len(ng_rows))
            ng_y_ref = Reference(ws, min_col=ng_col_start + 1, min_row=chart_data_start_row + 2,
                                max_row=chart_data_start_row + 1 + len(ng_rows))

            ng_series = Series(ng_y_ref, ng_x_ref, title="NG")
            ng_series.marker = Marker('circle')
            ng_series.marker.size = 5
            chart.series.append(ng_series)
        
        # 只有在有数据系列时才添加图表
        if len(chart.series) == 0:
            print("警告：没有有效的数据系列，跳过图表创建")
        else:
            # 设置图表位置和大小
            chart.width = 15
            chart.height = 10
            chart.legend.position = 'r'

            # 简化的轴设置
            chart.x_axis.title = '序号'
            chart.y_axis.title = '数值'

            # 添加图表到工作表
            ws.add_chart(chart, "M10")

        # 调整列宽
        ws.column_dimensions[chr(ord('A') + stats_col - 1)].width = 20

        # 保存为新文件
        output_file = input_file.replace('.xlsx', '_带图表.xlsx')
        wb.save(output_file)

        print(f"新文件已创建：{output_file}")
        print("包含原始数据、统计信息和图表")

        return valid_rows
        
    except Exception as e:
        print(f"创建图表时出错：{e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    # 输入文件路径
    input_file = '数据.xlsx'
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：找不到文件 {input_file}")
        return
    
    # 读取数据
    df = read_excel_data(input_file)
    if df is None:
        return
    
    # 显示前几行数据用于调试
    print("\n前5行数据预览：")
    print(df.head())
    
    # 创建安全的闪点图表
    result = create_flash_chart_safe(df, input_file)
    
    if result is not None:
        print("\n图表生成完成！")
        print(f"有效数据点：{len(result)}")
        print("\n请打开原Excel文件查看图表")

if __name__ == "__main__":
    main()